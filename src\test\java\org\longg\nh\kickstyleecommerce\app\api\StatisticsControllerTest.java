package org.longg.nh.kickstyleecommerce.app.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.longg.nh.kickstyleecommerce.domain.dtos.responses.statistics.ProductSalesResponse;
import org.longg.nh.kickstyleecommerce.domain.dtos.responses.statistics.RevenueStatResponse;
import org.longg.nh.kickstyleecommerce.domain.services.statistics.StatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(StatisticsController.class)
class StatisticsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private StatisticsService statisticsService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testGetDailyRevenueWithDateRange() throws Exception {
        // Arrange
        LocalDate startDate = LocalDate.of(2024, 1, 1);
        LocalDate endDate = LocalDate.of(2024, 1, 31);
        
        RevenueStatResponse mockResponse = RevenueStatResponse.builder()
                .period("2024-01-01 to 2024-01-31")
                .totalRevenue(new BigDecimal("1000000"))
                .totalOrders(50L)
                .periodType("RANGE")
                .build();

        when(statisticsService.getTotalRevenueByDateRange(startDate, endDate))
                .thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get("/api/v1/statistics/revenue/daily")
                        .param("startDate", "2024-01-01")
                        .param("endDate", "2024-01-31"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.period").value("2024-01-01 to 2024-01-31"))
                .andExpect(jsonPath("$.totalRevenue").value(1000000))
                .andExpect(jsonPath("$.totalOrders").value(50))
                .andExpect(jsonPath("$.periodType").value("RANGE"));
    }

    @Test
    void testGetDailyRevenueWithInvalidDateRange() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/api/v1/statistics/revenue/daily")
                        .param("startDate", "2024-01-31")
                        .param("endDate", "2024-01-01"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void testGetMonthlyRevenueWithDateRange() throws Exception {
        // Arrange
        LocalDate startDate = LocalDate.of(2024, 1, 1);
        LocalDate endDate = LocalDate.of(2024, 3, 31);
        
        RevenueStatResponse mockResponse = RevenueStatResponse.builder()
                .period("2024-01-01 to 2024-03-31")
                .totalRevenue(new BigDecimal("3000000"))
                .totalOrders(150L)
                .periodType("RANGE")
                .build();

        when(statisticsService.getTotalRevenueByDateRange(startDate, endDate))
                .thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get("/api/v1/statistics/revenue/monthly")
                        .param("startDate", "2024-01-01")
                        .param("endDate", "2024-03-31"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.periodType").value("RANGE"));
    }

    @Test
    void testGetTopSellingProductsDailyWithDateRange() throws Exception {
        // Arrange
        LocalDate startDate = LocalDate.of(2024, 1, 1);
        LocalDate endDate = LocalDate.of(2024, 1, 31);
        
        List<ProductSalesResponse> mockResponse = Arrays.asList(
                ProductSalesResponse.builder()
                        .productId(1L)
                        .productName("Product 1")
                        .totalQuantitySold(100L)
                        .totalRevenue(new BigDecimal("500000"))
                        .period("2024-01-01 to 2024-01-31")
                        .periodType("RANGE")
                        .build(),
                ProductSalesResponse.builder()
                        .productId(2L)
                        .productName("Product 2")
                        .totalQuantitySold(80L)
                        .totalRevenue(new BigDecimal("400000"))
                        .period("2024-01-01 to 2024-01-31")
                        .periodType("RANGE")
                        .build()
        );

        when(statisticsService.getTopSellingProductsByDateRange(startDate, endDate, 10))
                .thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get("/api/v1/statistics/products/top-selling/daily")
                        .param("startDate", "2024-01-01")
                        .param("endDate", "2024-01-31")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].productId").value(1))
                .andExpect(jsonPath("$[0].productName").value("Product 1"))
                .andExpect(jsonPath("$[0].totalQuantitySold").value(100))
                .andExpect(jsonPath("$[0].periodType").value("RANGE"));
    }

    @Test
    void testGetDailyRevenueWithSpecificDate() throws Exception {
        // Arrange
        LocalDate date = LocalDate.of(2024, 1, 15);
        
        RevenueStatResponse mockResponse = RevenueStatResponse.builder()
                .period("2024-01-15")
                .totalRevenue(new BigDecimal("50000"))
                .totalOrders(5L)
                .periodType("DAY")
                .build();

        when(statisticsService.getTotalRevenueByDate(date))
                .thenReturn(mockResponse);

        // Act & Assert
        mockMvc.perform(get("/api/v1/statistics/revenue/daily")
                        .param("date", "2024-01-15"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.period").value("2024-01-15"))
                .andExpect(jsonPath("$.periodType").value("DAY"));
    }

    @Test
    void testGetDailyRevenueWithMissingParameters() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/api/v1/statistics/revenue/daily"))
                .andExpect(status().isBadRequest());
    }
}
