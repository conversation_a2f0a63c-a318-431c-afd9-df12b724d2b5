server:
  port: ${SERVER_PORT}
  servlet:
    context-path: /kick-style
spring:
  application:
    environment: ${ENVIRONMENT:dev}
    name: kickstyle-ecommerce

  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://localhost:27017}
      database: ${MONGODB_DATABASE:kickstyle}

  datasource:
    url: ${POSTGRESQL_DATABASE_URL}
    username: ${POSTGRESQL_USERNAME}
    password: ${POSTGRESQL_PASSWORD}
    driver-class-name: org.postgresql.Driver
    hikari:
      connection-timeout: ${DB_CONNECTION_TIMEOUT:30000}
      maximum-pool-size: ${DB_MAX_POOL_SIZE:10}
      max-lifetime: ${DB_MAX_LIFETIME:1800000}

  jpa:
    show-sql: ${DEBUG:true}
    open-in-view: false
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        enable_lazy_load_no_trans: true
        temp:
          use_jdbc_metadata_defaults: false
        jdbc:
          lob:
            non_contextual_creation: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
        show_sql: ${DEBUG:true}

  mvc:
    dispatch-options-request: true

  main:
    allow-bean-definition-overriding: true

  # Mail configuration
  mail:
    host: ${MAIL_HOST:smtp.gmail.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME}
    password: ${MAIL_PASSWORD}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
          connectiontimeout: 5000
          timeout: 3000
          writetimeout: 5000

async:
  config:
    core-pool-size: ${ASYNC_CORE_POOL_SIZE:3}

logging:
  level:
    org.springframework.boot.autoconfigure: INFO
    org.springframework.web: DEBUG
    root: INFO

springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    use-root-path: true
    enabled: true
  packages-to-scan: org.longg.nh.kickstyleecommerce.app.api

# Application specific configuration
app:
  name: KickStyle E-commerce
  frontend:
    url: ${FRONTEND_URL:http://localhost:3000}
  jwt:
    secret: ${JWT_SECRET:mySecretKey12345678901234567890123456789012345678901234567890}
    access-token-expiration: ${JWT_ACCESS_TOKEN_EXPIRATION:86400} # 24 hours in seconds
    verification-token-expiration: ${JWT_VERIFICATION_TOKEN_EXPIRATION:3600} # 1 hour in seconds
  mail:
    from: ${MAIL_FROM:<EMAIL>}
    from-name: ${MAIL_FROM_NAME:KickStyle E-commerce}

upload:
  dir: ${UPLOAD_DIR}
url:
  server: ${URL_SERVER}
  port: ${PORT_SERVER}
  host: ${HOST_SERVER}

vnpay:
  url: ${VNPAY_URL}
  return:
    url: ${RETURN_URL}
  tmn:
    code: ${TMN_CODE}
  secret:
    key: ${SECRET_KEY}
  api:
    url: ${VNPAY_API_URL}
  version: ${VNPAY_VERSION}
  command: ${COMMAND}
