package org.longg.nh.kickstyleecommerce.infrastructure.config;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.security.SecuritySchemes;
import io.swagger.v3.oas.annotations.servers.Server;
import org.springframework.context.annotation.Configuration;

@Configuration
@OpenAPIDefinition(
    info = @Info(title = "Affiliate API", version = "1.0", description = "Affiliate Service API"),
    servers = {
      @Server(url = "http://*************:8386/kick-style", description = "host"),
      @Server(url = "http://localhost:8386/kick-style", description = "local")
    },
    security = @SecurityRequirement(name = "bearerAuth"))
@SecuritySchemes({
  @SecurityScheme(
      name = "bearerAuth",
      type = SecuritySchemeType.HTTP,
      scheme = "bearer",
      bearerFormat = "JWT",
      description = "Enter JWT Bearer Token here")
})
public class OpenApiConfig {}
