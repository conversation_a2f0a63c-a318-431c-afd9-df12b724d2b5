-- Tạo schema payments nếu chưa có
CREATE SCHEMA IF NOT EXISTS payments;

-- Tạo bảng payment_method
CREATE TABLE payments.payment_method
(
    id         BIGSERIAL PRIMARY KEY,
    name       VARCHAR(255) NOT NULL,
    slug       VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_deleted BOOLEAN   DEFAULT FALSE
);

-- Thêm dữ liệu mặc định cho các phương thức thanh toán
INSERT INTO payments.payment_method (name, slug) VALUES
('<PERSON><PERSON> toán khi nhận hàng (COD)', 'cod'),
('<PERSON>yển khoản ngân hàng', 'bank-transfer'),
('<PERSON><PERSON> điện tử MoMo', 'momo'),
('<PERSON><PERSON> điện tử ZaloPay', 'zalopay'),
('Thẻ tín dụng/ghi nợ', 'credit-card'),
('<PERSON><PERSON> điện tử VNPay', 'vnpay'); 