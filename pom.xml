<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.6</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>org.longg.nh</groupId>
    <artifactId>kickstyle-ecommerce</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>kickstyle-ecommerce</name>
    <description>kickstyle-ecommerce</description>
    <url/>
    <licenses>
        <license/>
    </licenses>
    <developers>
        <developer/>
    </developers>
    <scm>
        <connection/>
        <developerConnection/>
        <tag/>
        <url/>
    </scm>


    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <repositories>
        <repository>
            <id>jitpack.io</id>
            <url>https://jitpack.io</url>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.itextpdf/itext7-core -->
        <!-- https://mvnrepository.com/artifact/com.itextpdf/itext7-core -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext7-core</artifactId>
            <version>7.2.0</version>
            <type>pom</type>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.itextpdf/itextpdf -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13.4</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.springdoc/springdoc-openapi-starter-webmvc-ui -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.8.8</version>
        </dependency>

        <dependency>
            <groupId>com.github.Long23112002</groupId>
            <artifactId>shared-library</artifactId>
            <version>1.0.6</version>
        </dependency>

        <!-- Spring Security for password encoding -->
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
        </dependency>

        <!-- Spring Boot Mail -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- JWT dependencies -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>0.12.3</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>0.12.3</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>0.12.3</version>
            <scope>runtime</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>

            <plugin>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-maven-plugin</artifactId>
                <version>4.0.3</version>
                <executions>
                    <execution>
                        <id>users</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>migrate</goal>
                        </goals>
                        <configuration>
                            <configFile>services/users/config/dev/flyway.conf</configFile>
                        </configuration>
                    </execution>

                    <execution>
                        <id>products</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>migrate</goal>
                        </goals>
                        <configuration>
                            <configFile>services/products/config/dev/flyway.conf</configFile>
                        </configuration>
                    </execution>

                    <execution>
                        <id>reviews</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>migrate</goal>
                        </goals>
                        <configuration>
                            <configFile>services/reviews/config/dev/flyway.conf</configFile>
                        </configuration>
                    </execution>


                    <execution>
                        <id>carts</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>migrate</goal>
                        </goals>
                        <configuration>
                            <configFile>services/carts/config/dev/flyway.conf</configFile>
                        </configuration>
                    </execution>

                    <execution>
                        <id>orders</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>migrate</goal>
                        </goals>
                        <configuration>
                            <configFile>services/orders/config/dev/flyway.conf</configFile>
                        </configuration>
                    </execution>

                    <execution>
                        <id>coupons</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>migrate</goal>
                        </goals>
                        <configuration>
                            <configFile>services/coupons/config/dev/flyway.conf</configFile>
                        </configuration>
                    </execution>

                    <execution>
                        <id>payments</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>migrate</goal>
                        </goals>
                        <configuration>
                            <configFile>services/payments/config/dev/flyway.conf</configFile>
                        </configuration>
                    </execution>

                    <execution>
                        <id>contacts</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>migrate</goal>
                        </goals>
                        <configuration>
                            <configFile>services/contacts/config/dev/flyway.conf</configFile>
                        </configuration>
                    </execution>

                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <release>17</release>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.30</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
